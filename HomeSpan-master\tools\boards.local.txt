
###############################################################################
# DEFINE "HOMESPAN DEFAULTS" MENU THAT WILL OVERRIDE OTHER ARDUINO IDE SETTINGS 
#
#  - set Partition to "Minimal SPIFFS (1.9MB APP with OTA/190KB SPIFFS)"
#  - enable PSRAM if it is available
#  - override Upload Speed and any other board settings as needed 
#
###############################################################################

menu.HomeSpan=HomeSpan Defaults

featheresp32.menu.HomeSpan.enable=HomeSpan Defaults
featheresp32.menu.HomeSpan.enable.build.partitions=min_spiffs
featheresp32.menu.HomeSpan.enable.upload.maximum_size=1966080
featheresp32.menu.HomeSpan.disable=Disable Defaults

adafruit_feather_esp32_v2.menu.HomeSpan.enable=HomeSpan Defaults
adafruit_feather_esp32_v2.menu.HomeSpan.enable.build.partitions=min_spiffs
adafruit_feather_esp32_v2.menu.HomeSpan.enable.upload.maximum_size=1966080
adafruit_feather_esp32_v2.menu.HomeSpan.enable.build.defines=-DBOARD_HAS_PSRAM -mfix-esp32-psram-cache-issue -mfix-esp32-psram-cache-strategy=memw
adafruit_feather_esp32_v2.menu.HomeSpan.enable.upload.speed=460800
adafruit_feather_esp32_v2.menu.HomeSpan.disable=Disable Defaults

adafruit_feather_esp32s2.menu.HomeSpan.enable=HomeSpan Defaults
adafruit_feather_esp32s2.menu.HomeSpan.enable.build.partitions=min_spiffs
adafruit_feather_esp32s2.menu.HomeSpan.enable.upload.maximum_size=1966080
adafruit_feather_esp32s2.menu.HomeSpan.enable.build.defines=-DBOARD_HAS_PSRAM
adafruit_feather_esp32s2.menu.HomeSpan.disable=Disable Defaults

adafruit_feather_esp32s3.menu.HomeSpan.enable=HomeSpan Defaults
adafruit_feather_esp32s3.menu.HomeSpan.enable.build.partitions=min_spiffs
adafruit_feather_esp32s3.menu.HomeSpan.enable.upload.maximum_size=1966080
adafruit_feather_esp32s3.menu.HomeSpan.enable.build.defines=-DBOARD_HAS_PSRAM
adafruit_feather_esp32s3.menu.HomeSpan.enable.build.psram_type=qspi
adafruit_feather_esp32s3.menu.HomeSpan.disable=Disable Defaults

adafruit_qtpy_esp32_pico.menu.HomeSpan.enable=HomeSpan Defaults
adafruit_qtpy_esp32_pico.menu.HomeSpan.enable.build.partitions=min_spiffs
adafruit_qtpy_esp32_pico.menu.HomeSpan.enable.upload.maximum_size=1966080
adafruit_qtpy_esp32_pico.menu.HomeSpan.enable.build.defines=-DBOARD_HAS_PSRAM -mfix-esp32-psram-cache-issue -mfix-esp32-psram-cache-strategy=memw
adafruit_qtpy_esp32_pico.menu.HomeSpan.disable=Disable Defaults

adafruit_qtpy_esp32s2.menu.HomeSpan.enable=HomeSpan Defaults
adafruit_qtpy_esp32s2.menu.HomeSpan.enable.build.partitions=min_spiffs
adafruit_qtpy_esp32s2.menu.HomeSpan.enable.upload.maximum_size=1966080
adafruit_qtpy_esp32s2.menu.HomeSpan.enable.build.defines=-DBOARD_HAS_PSRAM
adafruit_qtpy_esp32s2.menu.HomeSpan.disable=Disable Defaults

wesp32.menu.HomeSpan.enable=HomeSpan Defaults
wesp32.menu.HomeSpan.enable.build.partitions=min_spiffs
wesp32.menu.HomeSpan.enable.upload.maximum_size=1966080
wesp32.menu.HomeSpan.disable=Disable Defaults

esp32s2.menu.HomeSpan.enable=HomeSpan Defaults
esp32s2.menu.HomeSpan.enable.build.partitions=min_spiffs
esp32s2.menu.HomeSpan.enable.upload.maximum_size=1966080
esp32s2.menu.HomeSpan.enable.build.defines=-DBOARD_HAS_PSRAM
esp32s2.menu.HomeSpan.disable=Disable Defaults

esp32s3.menu.HomeSpan.enable=HomeSpan Defaults
esp32s3.menu.HomeSpan.enable.build.partitions=min_spiffs
esp32s3.menu.HomeSpan.enable.upload.maximum_size=1966080
esp32s3.menu.HomeSpan.enable.build.defines=-DBOARD_HAS_PSRAM
esp32s3.menu.HomeSpan.enable.build.psram_type=opi
esp32s3.menu.HomeSpan.disable=Disable Defaults

esp32c3.menu.HomeSpan.enable=HomeSpan Defaults
esp32c3.menu.HomeSpan.enable.build.partitions=min_spiffs
esp32c3.menu.HomeSpan.enable.upload.maximum_size=1966080
esp32c3.menu.HomeSpan.disable=Disable Defaults

esp32c6.menu.HomeSpan.enable=HomeSpan Defaults
esp32c6.menu.HomeSpan.enable.build.partitions=min_spiffs
esp32c6.menu.HomeSpan.enable.upload.maximum_size=1966080
esp32c6.menu.HomeSpan.disable=Disable Defaults

##########################################
# ADD CONVENIENCE "FEATHER" PIN MAPPINGS #
##########################################

########################
# Adafruit ESP32 Feather
########################

# F13=13,F12=12,F27=27                                             // Digital w/Touch (3 pins)
# F15=15,F32=32,F14=14,F16=16,F17=17,F21=21                        // Digital Only (6 pins)
# F26=26,F25=25,F34=34,F39=39,F36=36,F4=4                          // A0-A5
# F22=22,F23=23                                                    // I2C SCL/SDA
# F5=5,F18=18,F19=19,F33=33                                        // SPI SCK/SDO/SDI/CS

featheresp32.compiler.cpp.extra_flags=-DF13=13 -DF12=12 -DF27=27 -DF15=15 -DF32=32 -DF14=14 -DF16=16 -DF17=17 -DF21=21 -DF26=26 -DF25=25 -DF34=34 -DF39=39 -DF36=36 -DF4=4 -DF22=22 -DF23=23 -DF5=5 -DF18=18 -DF19=19 -DF33=33 -DDEVICE_SUFFIX="" -DHS_FEATHER_PINS

####################
# ESP32S2 Dev Module
####################

# F13=1,F12=3,F27=7                                                // Digital w/Touch (3 pins)
# F15=10,F32=42,F14=11,F16=20,F17=21,F21=16                        // Digital Only (6 pins)
# F26=17,F25=14,F34=13,F39=12,F36=18,F4=19                         // A0-A5
# F22=9,F23=8                                                      // I2C SCL/SDA
# F5=36,F18=35,F19=37,F33=34                                       // SPI SCK/SDO/SDI/CS

esp32s2.compiler.cpp.extra_flags=-DF13=1 -DF12=3 -DF27=7 -DF15=10 -DF32=42 -DF14=11 -DF16=20 -DF17=21 -DF21=16 -DF26=17 -DF25=14 -DF34=13 -DF39=12 -DF36=18 -DF4=19  -DF22=9 -DF23=8 -DF5=36 -DF18=35 -DF19=37 -DF33=34 -DBUILTIN_PIXEL=18 -DDEVICE_SUFFIX="-S2" -DHS_FEATHER_PINS

####################
# ESP32C3 Dev Module
####################

# F27=19,F32=2,F14=10,F16=20,F17=21,F21=18                         // Digital Only (6 pins, but F16 and F17 used for Serial)
# F26=0,F25=1,F4=3                                                 // A0/A1/A5
# F22=9,F23=8                                                      // I2C SCL/SDA
# F5=4,F18=6,F19=5,F33=7                                           // SPI SCK/SDO/SDI/CS

esp32c3.compiler.cpp.extra_flags=-DF27=19 -DF32=2 -DF14=10 -DF16=20 -DF17=21 -DF21=18 -DF26=0 -DF25=1 -DF4=3 -DF22=9 -DF23=8 -DF5=4 -DF18=6 -DF19=5 -DF33=7 -DBUILTIN_PIXEL=8 -DDEVICE_SUFFIX="-C3" -DHS_FEATHER_PINS

###################
# ESP32S3 Dev Module
####################

# F13=5,F12=6,F27=7                                                // Digital w/Touch (3 pins)
# F15=16,F32=17,F14=18,F16=41,F17=40,F21=38                        // Digital Only (6 pins)
# F26=1,F25=2,F34=20,F39=19,F36=15,F4=4                            // A0-A5
# F22=9,F23=8                                                      // I2C SCL/SDA
# F5=12,F18=11,F19=13,F33=10                                       // SPI SCK/SDO/SDI/CS

esp32s3.compiler.cpp.extra_flags=-DF13=5 -DF12=6 -DF27=7 -DF15=16 -DF32=17 -DF14=18 -DF16=41 -DF17=40 -DF21=38 -DF26=1 -DF25=2 -DF34=20 -DF39=19 -DF36=15 -DF4=4 -DF22=9 -DF23=8 -DF5=12 -DF18=11 -DF19=13 -DF33=10 -DBUILTIN_PIXEL=48 -DDEVICE_SUFFIX="-S3" -DHS_FEATHER_PINS

#################### 
# ESP32C6 Dev Module
####################

# F12=9,F27=6,F15=7,F32=10,F14=11,F16=13,F17=12,F21=15             // Digital Only (8 pins)
# F26=3,F25=2,F34=1,F39=0,F36=5,F4=4                               // A0-A5
# F22=22,F23=23                                                    // I2C SCL/SDA
# F5=21,F18=19,F19=20,F33=18                                       // SPI SCK/SDO/SDI/CS

esp32c6.compiler.cpp.extra_flags=-DF12=9 -DF27=6 -DF15=7 -DF32=10 -DF14=11 -DF16=13 -DF17=12 -DF21=15 -DF26=3 -DF25=2 -DF34=1 -DF39=0 -DF36=5 -DF4=4 -DF22=22 -DF23=23 -DF5=21 -DF18=19 -DF19=20 -DF33=18 -DBUILTIN_PIXEL=8 -DDEVICE_SUFFIX="-C6" -DHS_FEATHER_PINS

######################
# Silicognition wESP32
######################

# F12=2,F27=32                                                     // Digital w/Touch (2 pins)
# F26=12,F25=14,F32=18,F14=23,F21=5,F33=13                         // Digital Only (6 pins)
# F34=35,F39=34,F36=36,F4=33                                       // A2-A5
# F22=4,F23=15                                                     // I2C SCL/SDA

wesp32.compiler.cpp.extra_flags=-DF12=2 -DF27=32 -DF26=12 -DF25=14 -DF32=18 -DF14=23 -DF21=5 -DF33=13 -DF34=35 -DF39=34 -DF36=36 -DF4=33 -DF22=4 -DF23=15 -DDEVICE_SUFFIX="-WESP32" -DHS_FEATHER_PINS

##########################################
# HIDE ALL OTHER BOARDS FROM ARDUINO IDE #
##########################################

esp32_family.hide=true
esp32c2.hide=true
esp32c5.hide=true
esp32p4.hide=true
esp32h2.hide=true
esp32.hide=true
esp32da.hide=true
esp32wrover.hide=true
pico32.hide=true
esp32s3-octal.hide=true
esp32s3box.hide=true
esp32s3usbotg.hide=true
esp32s3camlcd.hide=true
esp32s2usb.hide=true
esp32wroverkit.hide=true
aventen_s3_sync.hide=true
BharatPi-Node-Wifi.hide=true
BharatPi-A7672S-4G.hide=true
BharatPi-LoRa.hide=true
um_bling.hide=true
um_edges3_d.hide=true
um_feathers2.hide=true
um_feathers2neo.hide=true
um_feathers3.hide=true
um_feathers3neo.hide=true
um_nanos3.hide=true
um_omgs3.hide=true
um_pros3.hide=true
um_squixl.hide=true
um_tinypico.hide=true
um_tinyc6.hide=true
um_tinys2.hide=true
um_tinys3.hide=true
S_ODI_Ultra.hide=true
lilygo_t_display.hide=true
lilygo_t_display_s3.hide=true
lilygo_t_eth_lite.hide=true
lilygo_t3s3.hide=true
twatchs3.hide=true
twatch_ultra.hide=true
tlora_pager.hide=true
micros2.hide=true
magicbit.hide=true
turta_iot_node.hide=true
ttgo-lora32.hide=true
ttgo-t1.hide=true
ttgo-t7-v13-mini32.hide=true
ttgo-t7-v14-mini32.hide=true
ttgo-t-oi-plus.hide=true
cw02.hide=true
esp32thing.hide=true
esp32thing_plus.hide=true
esp32thing_plus_c.hide=true
sparkfun_esp32s2_thing_plus.hide=true
sparkfun_esp32s3_thing_plus.hide=true
sparkfun_esp32c6_thing_plus.hide=true
esp32micromod.hide=true
sparkfun_lora_gateway_1-channel.hide=true
sparkfun_esp32_iot_redboard.hide=true
sparkfun_esp32c6_qwiic_pocket.hide=true
sparkfun_pro_micro_esp32c3.hide=true
nina_w10.hide=true
nora_w10.hide=true
widora-air.hide=true
esp320.hide=true
nano32.hide=true
d32.hide=true
d32_pro.hide=true
lolin_c3_mini.hide=true
lolin_c3_pico.hide=true
lolin_s2_mini.hide=true
lolin_s2_pico.hide=true
lolin_s3.hide=true
lolin_s3_mini.hide=true
lolin_s3_mini_pro.hide=true
lolin_s3_pro.hide=true
lolin32.hide=true
viralink32g01.hide=true
viralink32g11.hide=true
lolin32-lite.hide=true
pocket_32.hide=true
WeMosBat.hide=true
espea32.hide=true
quantum.hide=true
node32s.hide=true
hornbill32dev.hide=true
hornbill32minima.hide=true
dfrobot_beetle_esp32c3.hide=true
dfrobot_beetle_esp32c6.hide=true
dfrobot_firebeetle2_esp32e.hide=true
dfrobot_firebeetle2_esp32s3.hide=true
dfrobot_firebeetle2_esp32c6.hide=true
dfrobot_romeo_esp32s3.hide=true
dfrobot_lorawan_esp32s3.hide=true
firebeetle32.hide=true
intorobot-fig.hide=true
onehorse32dev.hide=true
adafruit_metro_esp32s2.hide=true
adafruit_metro_esp32s3.hide=true
adafruit_magtag29_esp32s2.hide=true
adafruit_funhouse_esp32s2.hide=true
adafruit_feather_esp32s2_tft.hide=true
adafruit_feather_esp32s2_reversetft.hide=true
adafruit_feather_esp32s3_nopsram.hide=true
adafruit_feather_esp32s3_tft.hide=true
adafruit_feather_esp32s3_reversetft.hide=true
adafruit_feather_esp32c6.hide=true
adafruit_qtpy_esp32c3.hide=true
adafruit_qtpy_esp32s3_nopsram.hide=true
adafruit_qtpy_esp32s3_n4r2.hide=true
adafruit_itsybitsy_esp32.hide=true
adafruit_matrixportal_esp32s3.hide=true
adafruit_camera_esp32s3.hide=true
adafruit_qualia_s3_rgb666.hide=true
sparklemotion.hide=true
sparklemotionmini.hide=true
sparklemotionstick.hide=true
nodemcu-32s.hide=true
nologo_esp32c3_super_mini.hide=true
nologo_esp32s3_pico.hide=true
mhetesp32devkit.hide=true
mhetesp32minikit.hide=true
esp32vn-iot-uno.hide=true
esp32doit-devkit-v1.hide=true
esp32doit-espduino.hide=true
esp32-evb.hide=true
esp32-gateway.hide=true
esp32-poe.hide=true
esp32-poe-iso.hide=true
esp32-devkitlipo.hide=true
esp32s2-devkitlipo.hide=true
esp32s2-devkitlipo-usb.hide=true
esp32s3-devkitlipo.hide=true
esp32c3-devkitlipo.hide=true
esp32c6-evb.hide=true
esp32h2-devkitlipo.hide=true
esp32-sbc-fabgl.hide=true
espino32.hide=true
m5stack_core.hide=true
m5stack_fire.hide=true
m5stack_core2.hide=true
m5stack_tough.hide=true
m5stack_station.hide=true
m5stack_stickc.hide=true
m5stack_stickc_plus.hide=true
m5stack_stickc_plus2.hide=true
m5stack_atom.hide=true
m5stack_atoms3.hide=true
m5stack_cores3.hide=true
m5stack_timer_cam.hide=true
m5stack_unit_cam.hide=true
m5stack_unit_cams3.hide=true
m5stack_poe_cam.hide=true
m5stack_paper.hide=true
m5stack_coreink.hide=true
m5stack_stamp_pico.hide=true
m5stack_stamp_c3.hide=true
m5stack_stamp_s3.hide=true
m5stack_capsule.hide=true
m5stack_cardputer.hide=true
m5stack_dial.hide=true
m5stack_dinmeter.hide=true
m5stack_nanoc6.hide=true
odroid_esp32.hide=true
heltec_wifi_kit_32.hide=true
heltec_wifi_kit_32_V3.hide=true
heltec_wifi_lora_32.hide=true
heltec_wifi_lora_32_V2.hide=true
heltec_wifi_lora_32_V3.hide=true
heltec_wireless_stick_V3.hide=true
heltec_wireless_stick_lite_V3.hide=true
heltec_wireless_shell_V3.hide=true
heltec_capsule_sensor_V3.hide=true
heltec_wireless_paper.hide=true
heltec_wireless_tracker.hide=true
heltec_wireless_mini_shell.hide=true
heltec_wireless_stick.hide=true
heltec_wireless_stick_lite.hide=true
heltec_wireless_bridge.hide=true
heltec_ht_de01.hide=true
heltec_vision_master_e290.hide=true
heltec_vision_master_t190.hide=true
heltec_vision_master_e_213.hide=true
espectro32.hide=true
CoreESP32.hide=true
alksesp32.hide=true
wipy3.hide=true
wt32-eth01.hide=true
wt32-sc01-plus.hide=true
bpi-bit.hide=true
bpi_leaf_s3.hide=true
t-beam.hide=true
d-duino-32.hide=true
lopy.hide=true
lopy4.hide=true
oroca_edubot.hide=true
fm-devkit.hide=true
fri3d_2024_esp32s3.hide=true
frogboard.hide=true
esp32cam.hide=true
twatch.hide=true
d1_mini32.hide=true
d1_uno32.hide=true
gpy.hide=true
vintlabs-devkit-v1.hide=true
honeylemon.hide=true
mgbot-iotik32a.hide=true
mgbot-iotik32b.hide=true
piranha_esp-32.hide=true
metro_esp-32.hide=true
sensesiot_weizen.hide=true
kits-edu.hide=true
mPython.hide=true
OpenKB.hide=true
wifiduino32.hide=true
wifiduino32c3.hide=true
wifiduino32s3.hide=true
imbrios-logsens-v1p1.hide=true
healthypi4.hide=true
ET-Board.hide=true
ch_denky.hide=true
uPesy_wrover.hide=true
uPesy_wroom.hide=true
uPesy_edu_esp32.hide=true
upesy_esp32c3_basic.hide=true
upesy_esp32c3_mini.hide=true
upesy_esp32s3_basic.hide=true
kb32.hide=true
deneyapkart.hide=true
deneyapkartv2.hide=true
deneyapkart1A.hide=true
deneyapkart1Av2.hide=true
deneyapmini.hide=true
deneyapminiv2.hide=true
deneyapkartg.hide=true
esp32-trueverit-iot-driver.hide=true
esp32-trueverit-iot-driver-mkii.hide=true
atmegazero_esp32s2.hide=true
franzininho_wifi_esp32s2.hide=true
franzininho_wifi_msc_esp32s2.hide=true
tamc_termod_s3.hide=true
dpu_esp32.hide=true
sonoff_dualr3.hide=true
lionbit.hide=true
watchy.hide=true
AirM2M_CORE_ESP32C3.hide=true
XIAO_ESP32C3.hide=true
XIAO_ESP32C6.hide=true
XIAO_ESP32S3.hide=true
XIAO_ESP32S3_Plus.hide=true
connaxio_espoir.hide=true
aw2eth.hide=true
department_of_alchemy_minimain_esp32s2.hide=true
Bee_Data_Logger.hide=true
Bee_Motion_S3.hide=true
Bee_Motion.hide=true
Bee_Motion_Mini.hide=true
Bee_S3.hide=true
unphone7.hide=true
unphone8.hide=true
unphone9.hide=true
cytron_maker_feather_aiot_s3.hide=true
redpill_esp32s3.hide=true
esp32c3m1IKit.hide=true
roboheart_hercules.hide=true
VALTRACK_V4_VTS_ESP32_C3.hide=true
VALTRACK_V4_MFW_ESP32_C3.hide=true
Edgebox-ESP-100.hide=true
crabik_slot_esp32_s3.hide=true
nebulas3.hide=true
lionbits3.hide=true
gen4-ESP32-S3R8n16.hide=true
namino_rosso.hide=true
namino_arancio.hide=true
namino_bianco.hide=true
ioxesp32.hide=true
ioxesp32ps.hide=true
ioxesp32c6.hide=true
atd147_s3.hide=true
atd35s3.hide=true
esp32s3_powerfeather.hide=true
sensebox_mcu_esp32s2.hide=true
nano_nora.hide=true
makergo_c3_supermini.hide=true
epulse_feather.hide=true
epulse_feather_c6.hide=true
Geekble_ESP32C3.hide=true
Geekble_Nano_ESP32S3.hide=true
waveshare_esp32_s3_zero.hide=true
ws_esp32_s3_matrix.hide=true
waveshare_esp32_s3_touch_lcd_169.hide=true
waveshare_esp32_s3_touch_amoled_18.hide=true
waveshare_esp32_s3_lcd_169.hide=true
waveshare_esp32s3_touch_lcd_128.hide=true
weact_studio_esp32c3.hide=true
aslcanx2.hide=true
walter.hide=true
elecrow_crowpanel_7.hide=true
circuitart_zero_s3.hide=true
alfredo-nou3.hide=true
codecell.hide=true
jczn_2432s028r.hide=true
waveshare_esp32_s3_touch_amoled_241.hide=true
waveshare_esp32_s3_touch_lcd_43.hide=true
waveshare_esp32_s3_touch_lcd_43B.hide=true
waveshare_esp32_s3_touch_lcd_7.hide=true
waveshare_esp32_s3_touch_lcd_5.hide=true
waveshare_esp32_s3_touch_lcd_5B.hide=true
waveshare_esp32_s3_touch_lcd_4.hide=true
waveshare_esp32_s3_touch_lcd_185.hide=true
cezerio_dev_esp32c6.hide=true
cezerio_mini_dev_esp32c6.hide=true
waveshare_esp32_s3_lcd_185.hide=true
waveshare_esp32_s3_touch_lcd_146.hide=true
waveshare_esp32_s3_lcd_146.hide=true
waveshare_esp32_s3_touch_lcd_185_box.hide=true
waveshare_esp32_s3_lcd_147.hide=true
waveshare_esp32_s3_touch_lcd_21.hide=true
waveshare_esp32_s3_touch_lcd_28.hide=true
waveshare_esp32_s3_relay_6ch.hide=true
waveshare_esp32_s3_touch_amoled_164.hide=true
waveshare_esp32_s3_touch_amoled_143.hide=true
waveshare_esp32_s3_touch_amoled_191.hide=true
Pcbcupid_GLYPH_C3.hide=true
Pcbcupid_GLYPH_H2.hide=true
Pcbcupid_GLYPH_C6.hide=true
yb_esp32s3_amp_v2.hide=true
yb_esp32s3_amp_v3.hide=true
yb_esp32s3_eth.hide=true
yb_esp32s3_drv.hide=true
huidu_hd_wf2.hide=true
huidu_hd_wf4.hide=true
cyobot_v2_esp32s3.hide=true
rakwireless_rak3112.hide=true
kodedot.hide=true
fed4.hide=true

name=HomeSpan
version=2.1.5
author=<PERSON> <<EMAIL>>
maintainer=<PERSON> <<EMAIL>>
sentence=A robust and extremely easy-to-use HomeKit implementation for the Espressif ESP32 running on the Arduino IDE.
paragraph=This library provides a microcontroller-focused implementation of Apple's HomeKit Accessory Protocol (HAP - Release R2) designed specifically for the ESP32 running on the Arduino IDE.  HomeSpan pairs directly to iOS Home via WiFi or Ethernet without the need for any external bridges or components.  Supports the original ESP32 as well as the S2, S3, C3 and C6.
url=https://github.com/HomeSpan/HomeSpan
architectures=esp32
includes=HomeSpan.h

/*********************************************************************************
 *  MIT License
 *  
 *  Copyright (c) 2020-2024 Gregg <PERSON>
 *  
 *  https://github.com/HomeSpan/HomeSpan
 *  
 *  Permission is hereby granted, free of charge, to any person obtaining a copy
 *  of this software and associated documentation files (the "Software"), to deal
 *  in the Software without restriction, including without limitation the rights
 *  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 *  copies of the Software, and to permit persons to whom the Software is
 *  furnished to do so, subject to the following conditions:
 *  
 *  The above copyright notice and this permission notice shall be included in all
 *  copies or substantial portions of the Software.
 *  
 *  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 *  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 *  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 *  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 *  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 *  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 *  SOFTWARE.
 *  
 ********************************************************************************/
 
////////////////////////////////////////////////////////////////
//                                                            //
//    HomeSpan: A HomeKit implementation for the ESP32        //
//    ------------------------------------------------        //
//                                                            //
// Example 11: Service Names:                                 //
//             * setting the names of individual Services     //
//             * "changing" the icons in a bridge Accessory   //
//                                                            //
////////////////////////////////////////////////////////////////

#include "HomeSpan.h" 

  // INITIAL NOTE:  Apple is constantly updating how the Home App Icons are chosen and how/if/where/when the Names for
  //                Accessories and Services are displayed.  This example has been tested and verified as of iOS 17.2.1.

void setup() {

  // As described in previous examples, when pairing a device the Home App will choose default names for each
  // Accessory Tile, unless you override those default names with your own names by adding a Name Characteristic
  // to the Accessory Information Service for each Accessory (except the first, which is typically the Bridge Accessory).

  // The same process holds true for the names of the Services in an Accessory with multiple Services: if a Service is not named,
  // the Home App will generate one. You can of course change the names of individual Services when prompted
  // during the pairing process, or at any time after pairing from within the appropriate settings pages in the Home App.
  
  // But more importantly, you can name Services in your sketch so that those name show up when pairing, saving you the need to
  // rename them from the settings pages in the Home App.

  // Whereas we previously used the *Name* Characteristic to provide names for Accessory Tiles, we use the *ConfiguredName* Characteristic
  // to provide names for individual Services within each Accessory.
  
  // One important distinction between Name and ConfigureName is that Name is only used by the Home App during pairing.  After that,
  // any changes you make to the name of an Accessory Tile from within the Home App are never communicated back to HomeSpan, and any changes
  // you might make to those names in your sketch will not be reflected in the Home App unless you unpair and re-pair the device.  In contrast,
  // ConfiguredName works like any other Characteristic:  changes made to ConfiguredName from within a sketch are proporgated to the Home App,
  // and any edits you make to a Service's name in the Home App trigger a corresponding call to update() in HomeSpan so HomeSpan and the Home App
  // are always in sync with regard to the names of any Services that includes the ConfiguredName Characteristic.

  // NOTE: Service names (whether those generated by the Home App or specified via the ConfiguredName Characteristic) are only displayed on the
  // control screen of an Accessory Tile if there are two more more Services of the same type.  But even if a Service name does not appear in the Home App,
  // it will still be used by Siri to control a specific Service within an Accessory by voice.

  // In the example below we create 5 different functional Accessories, each illustrating how names, as well as icons, are chosen by the Home App
  
  Serial.begin(115200);

  // This device will be configured as a Bridge, with the Category set to Bridges

  homeSpan.begin(Category::Bridges,"HomeSpan Bridge");

  // Our initial Accessory is therefore the "Bridge" Accessory
  
  new SpanAccessory();  
    new Service::AccessoryInformation();
      new Characteristic::Identify(); 

  // Our first "functional" Accessory is a combination of a LightBulb, Outlet, and Switch.  Note that when pairing, the Home App generates
  // default names of "Light", "Outlet", and "Switch" for these three Services, though these names are NOT displayed on the control screen
  // of the Accessory since there is only one type of each Service.  Also note that the Home App selects a LightBulb icon for the Accessory Tile
  
  new SpanAccessory();  
    new Service::AccessoryInformation();
      new Characteristic::Identify(); 
      new Characteristic::Name("Light First");       // this sets the name of the Accessory Tile
    new Service::LightBulb();                        // the icon of the Accessory Tile will be a Lightbulb, since this is the first functional Service
      new Characteristic::On();
    new Service::Outlet();
      new Characteristic::On();
      new Characteristic::OutletInUse();
    new Service::Switch();
      new Characteristic::On();

  // Our second Accessory is similar to the first, but here we define the Switch Service first.  Note that the Home App now selects
  // a Switch icon for the Accessory Tile

    new SpanAccessory();  
    new Service::AccessoryInformation();
      new Characteristic::Identify(); 
      new Characteristic::Name("Switch First");      // this sets the name of the Accessory Tile
    new Service::Switch();                           // the icon of the Accessory Tile will be a Switch, since this is the first functional Service
      new Characteristic::On();      
    new Service::Outlet();
      new Characteristic::On();
      new Characteristic::OutletInUse();
    new Service::LightBulb();
      new Characteristic::On();

  // Our third Accessory is similar to the second, but here we define 2 Switches, 2 LightBulbs, but still only 1 Outlet.  This time, during pairing
  // the Home App generates default names of Switch, Switch 2, Light, Light 2, and Outlet.  Importantly, note that on the control screen for
  // this Accessory, the Home App now displays the names of the Switches ("Switch" and "Switch 2") as well as the LightBulbs ("Light" and "Light 2")
  // under each corresponding control, but it does NOT display the name "Outlet" under the Outlet control since there is only one Outlet Service

    new SpanAccessory();  
    new Service::AccessoryInformation();
      new Characteristic::Identify(); 
      new Characteristic::Name("Two Switches");      // this sets the name of the Accessory Tile
    new Service::Switch();                           // the icon of the Accessory Tile will be a Switch, since this is the first functional Service
      new Characteristic::On();      
    new Service::Switch();
      new Characteristic::On();      
    new Service::Outlet();
      new Characteristic::On();
      new Characteristic::OutletInUse();
    new Service::LightBulb();
      new Characteristic::On();
    new Service::LightBulb();
      new Characteristic::On();

  // Our fourth and final Accessory is the same as the third, but this time we use the ConfiguredName Characteristic to define a name for each Service.
  // When pairing, you should see the Home App now uses the names below instead of generating default names as it did in the other examples.  You
  // should also see these names displayed under each control on the control screen for the Accessory, with the exception of the Outlet Service.
  // Though we did provide a name for the Outlet, since there is only one Outlet Service in this Accessory, the Home App does not display its name.
  // Howevever, if from the settings screen for this Accessory you further navigate to the "Accessories" page, you will indeed see the names for each
  // Service exactly as specified below, including the Outlet name "Aux Power"
  
    new SpanAccessory();  
    new Service::AccessoryInformation();
      new Characteristic::Identify();
      new Characteristic::Name("Central Control");    // this sets the name of the Accessory Tile
    new Service::Switch();                            // the icon of the Accessory Tile will be a Switch, since this is the first functional Service
      new Characteristic::On();
      new Characteristic::ConfiguredName("High Voltage");   // this sets the name of the first Switch Service
    new Service::Switch();
      new Characteristic::On();      
      new Characteristic::ConfiguredName("Low Voltage");    // this sets the name of the second Switch Service
    new Service::Outlet();
      new Characteristic::On();
      new Characteristic::OutletInUse();
      new Characteristic::ConfiguredName("Aux Power");      // this sets the name of the Outlet Service
    new Service::LightBulb();
      new Characteristic::On();
      new Characteristic::ConfiguredName("Main Lights");    // this sets the name of the first LightBulb Service
    new Service::LightBulb();
      new Characteristic::On();
      new Characteristic::ConfiguredName("Accent Lights");  // this sets the name of the second LightBulb Service


} // end of setup()

//////////////////////////////////////

void loop(){
  
  homeSpan.poll();
  
} // end of loop()
